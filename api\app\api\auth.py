from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from datetime import timedelta
from ..core.database import get_db
from ..core.security import create_access_token
from ..core.config import settings
from ..crud.admin import admin_crud
from ..schemas.auth import LoginRequest, LoginResponse, UserProfile
from ..api.deps import get_current_active_admin
from ..models.admin import Admin

router = APIRouter()


@router.post("/login", response_model=LoginResponse)
async def login(
    login_data: LoginRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """管理员登录"""
    # 验证用户凭据
    admin = admin_crud.authenticate(db, login_data.username, login_data.password)
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if admin.status != "1":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="账户已被禁用",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": admin.username, "user_id": admin.id},
        expires_delta=access_token_expires
    )
    
    # 更新登录信息
    client_ip = request.client.host if request.client else "unknown"
    admin_crud.update_login_info(db, admin.id, client_ip)
    
    # 构建用户信息
    user_info = {
        "id": admin.id,
        "username": admin.username,
        "nickname": admin.nickname,
        "email": admin.email,
        "mobile": admin.mobile,
        "avatar": admin.avatar,
        "status": admin.status,
        "last_login_time": admin.last_login_time,
        "last_login_ip": admin.last_login_ip
    }
    
    return LoginResponse(
        access_token=access_token,
        token_type="bearer",
        user_info=user_info
    )


@router.post("/logout")
async def logout(current_admin: Admin = Depends(get_current_active_admin)):
    """管理员登出"""
    return {"message": "Successfully logged out"}


@router.get("/profile", response_model=UserProfile)
async def get_profile(current_admin: Admin = Depends(get_current_active_admin)):
    """获取当前用户信息"""
    return UserProfile.model_validate(current_admin)


@router.post("/refresh")
async def refresh_token(current_admin: Admin = Depends(get_current_active_admin)):
    """刷新令牌"""
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": current_admin.username, "user_id": current_admin.id},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer"
    }
