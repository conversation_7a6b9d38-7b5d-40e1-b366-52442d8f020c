import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { storage } from '@/utils/storage'
import { login as loginApi, logout as logoutApi, getProfile } from '@/api/auth'
import type { LoginRequest, UserInfo } from '@/types/auth'
import { ElMessage } from 'element-plus'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(storage.getToken())
  const userInfo = ref<UserInfo | null>(storage.getUserInfo())
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userName = computed(() => userInfo.value?.nickname || userInfo.value?.username || '')

  // 登录
  const login = async (loginData: LoginRequest): Promise<boolean> => {
    try {
      isLoading.value = true
      const response = await loginApi(loginData)
      
      // 保存token和用户信息
      token.value = response.access_token
      userInfo.value = response.user_info
      
      storage.setToken(response.access_token)
      storage.setUserInfo(response.user_info)
      
      ElMessage.success('登录成功')
      return true
    } catch (error) {
      console.error('Login failed:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async (): Promise<void> => {
    try {
      if (token.value) {
        await logoutApi()
      }
    } catch (error) {
      console.error('Logout failed:', error)
    } finally {
      // 清除本地存储
      token.value = null
      userInfo.value = null
      storage.clear()
    }
  }

  // 获取用户信息
  const fetchUserInfo = async (): Promise<boolean> => {
    try {
      if (!token.value) return false
      
      const profile = await getProfile()
      userInfo.value = profile
      storage.setUserInfo(profile)
      return true
    } catch (error) {
      console.error('Fetch user info failed:', error)
      // 如果获取用户信息失败，可能token已过期，清除登录状态
      await logout()
      return false
    }
  }

  // 初始化认证状态
  const initAuth = async (): Promise<boolean> => {
    if (token.value) {
      return await fetchUserInfo()
    }
    return false
  }

  return {
    // 状态
    token,
    userInfo,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    userName,
    
    // 方法
    login,
    logout,
    fetchUserInfo,
    initAuth
  }
})
