#!/usr/bin/env python3
"""
数据库初始化脚本
创建测试管理员账户
"""
import time
from app.core.database import SessionLocal
from app.core.security import get_password_hash
from app.models.admin import Admin

def create_test_admin():
    """创建测试管理员账户"""
    db = SessionLocal()
    try:
        # 检查是否已存在admin用户
        existing_admin = db.query(Admin).filter(Admin.username == "admin").first()
        if existing_admin:
            print("测试管理员账户已存在")
            return
        
        # 创建测试管理员
        password, salt = get_password_hash("123456")
        current_time = int(time.time())
        
        admin = Admin(
            username="admin",
            nickname="超级管理员",
            email="<EMAIL>",
            mobile="13800138000",
            password=password,
            salt=salt,
            status="1",
            create_time=current_time,
            update_time=current_time
        )
        
        db.add(admin)
        db.commit()
        print("测试管理员账户创建成功")
        print("用户名: admin")
        print("密码: 123456")
        
    except Exception as e:
        print(f"创建测试管理员失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_test_admin()
