<template>
  <div class="admin-rule">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>权限规则管理</span>
          <el-button type="primary">
            <el-icon><Plus /></el-icon>
            添加权限规则
          </el-button>
        </div>
      </template>
      
      <el-empty description="功能开发中..." />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
</script>

<style scoped>
.admin-rule {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
