import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { usePermissionStore } from '@/stores/permission'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/LoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      name: 'Layout',
      component: () => import('@/views/layout/LayoutView.vue'),
      meta: { requiresAuth: true },
      redirect: '/dashboard',
      children: [
        {
          path: '/dashboard',
          name: 'Dashboard',
          component: () => import('@/views/dashboard/DashboardView.vue'),
          meta: { title: '仪表盘', icon: 'Dashboard' }
        },
        {
          path: '/admin',
          name: 'AdminManagement',
          meta: { title: '管理员管理', icon: 'User' },
          children: [
            {
              path: '/admin/list',
              name: 'AdminList',
              component: () => import('@/views/admin/AdminListView.vue'),
              meta: { title: '管理员列表' }
            },
            {
              path: '/admin/groups',
              name: 'AdminGroups',
              component: () => import('@/views/admin/AdminGroupView.vue'),
              meta: { title: '管理员组' }
            },
            {
              path: '/admin/rules',
              name: 'AdminRules',
              component: () => import('@/views/admin/AdminRuleView.vue'),
              meta: { title: '权限规则' }
            }
          ]
        },
        {
          path: '/users',
          name: 'UserManagement',
          component: () => import('@/views/user/UserListView.vue'),
          meta: { title: '用户管理', icon: 'UserFilled' }
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/error/NotFoundView.vue')
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  const permissionStore = usePermissionStore()

  // 如果是登录页面，且已经登录，则重定向到首页
  if (to.name === 'Login' && authStore.isLoggedIn) {
    next('/')
    return
  }

  // 如果需要认证的页面，但未登录，则重定向到登录页
  if (to.meta.requiresAuth && !authStore.isLoggedIn) {
    next('/login')
    return
  }

  // 如果已登录但权限未加载，则加载权限
  if (authStore.isLoggedIn && !permissionStore.isLoaded) {
    await permissionStore.fetchPermissions()
  }

  next()
})

export default router
