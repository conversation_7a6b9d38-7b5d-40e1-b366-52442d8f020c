from sqlalchemy import Column, Integer, String, Text, BigInteger
from ..core.database import Base


class Admin(Base):
    """管理员表"""
    __tablename__ = "ba_admin"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    username = Column(String(20), unique=True, nullable=False, default="")
    nickname = Column(String(50), nullable=False, default="")
    avatar = Column(String(255), nullable=False, default="")
    email = Column(String(50), nullable=False, default="")
    mobile = Column(String(11), nullable=False, default="")
    login_failure = Column(Integer, nullable=False, default=0)
    last_login_time = Column(BigInteger, nullable=True)
    last_login_ip = Column(String(50), nullable=False, default="")
    password = Column(String(32), nullable=False, default="")
    salt = Column(String(30), nullable=False, default="")
    motto = Column(String(255), nullable=False, default="")
    status = Column(String(1), nullable=False, default="1")  # '0'=禁用, '1'=启用
    update_time = Column(BigInteger, nullable=True)
    create_time = Column(BigInteger, nullable=True)


class AdminGroup(Base):
    """管理员组表"""
    __tablename__ = "ba_admin_group"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    pid = Column(Integer, nullable=False, default=0)
    name = Column(String(100), nullable=False, default="")
    rules = Column(Text, nullable=True)
    status = Column(String(1), nullable=False, default="1")  # '0'=禁用, '1'=启用
    update_time = Column(BigInteger, nullable=True)
    create_time = Column(BigInteger, nullable=True)


class AdminGroupAccess(Base):
    """管理员组权限关联表"""
    __tablename__ = "ba_admin_group_access"
    
    uid = Column(Integer, primary_key=True)
    group_id = Column(Integer, primary_key=True)


class AdminRule(Base):
    """权限规则表"""
    __tablename__ = "ba_admin_rule"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    pid = Column(Integer, nullable=False, default=0, index=True)
    type = Column(String(20), nullable=False, default="menu")  # menu_dir, menu, button
    title = Column(String(50), nullable=False, default="")
    name = Column(String(50), nullable=False, default="")
    path = Column(String(100), nullable=False, default="")
    icon = Column(String(50), nullable=False, default="")
    menu_type = Column(String(20), nullable=True)  # tab, link, iframe
    url = Column(String(255), nullable=False, default="")
    component = Column(String(100), nullable=False, default="")
    keepalive = Column(Integer, nullable=False, default=0)
    extend = Column(String(20), nullable=False, default="none")  # none, add_rules_only, add_menu_only
    remark = Column(String(255), nullable=False, default="")
    weigh = Column(Integer, nullable=False, default=0)
    status = Column(String(1), nullable=False, default="1")  # '0'=禁用, '1'=启用
    update_time = Column(BigInteger, nullable=True)
    create_time = Column(BigInteger, nullable=True)
