from pydantic import BaseModel
from typing import Optional


class LoginRequest(BaseModel):
    """登录请求"""
    username: str
    password: str


class LoginResponse(BaseModel):
    """登录响应"""
    access_token: str
    token_type: str = "bearer"
    user_info: dict


class TokenData(BaseModel):
    """Token数据"""
    username: Optional[str] = None
    user_id: Optional[int] = None


class UserProfile(BaseModel):
    """用户资料"""
    id: int
    username: str
    nickname: str
    email: str
    mobile: str
    avatar: str
    status: str
    last_login_time: Optional[int] = None
    last_login_ip: str
    
    class Config:
        from_attributes = True
