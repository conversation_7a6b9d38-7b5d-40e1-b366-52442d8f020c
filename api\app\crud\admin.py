from sqlalchemy.orm import Session
from typing import Optional, List
from ..models.admin import Admin, AdminGroup, AdminRule, AdminGroupAccess
from ..schemas.admin import AdminCreate, AdminUpdate, AdminGroupCreate, AdminGroupUpdate, AdminRuleCreate, AdminRuleUpdate
from ..core.security import get_password_hash, verify_password
import time


class AdminCRUD:
    def get_by_username(self, db: Session, username: str) -> Optional[Admin]:
        """根据用户名获取管理员"""
        return db.query(Admin).filter(Admin.username == username).first()
    
    def get_by_id(self, db: Session, admin_id: int) -> Optional[Admin]:
        """根据ID获取管理员"""
        return db.query(Admin).filter(Admin.id == admin_id).first()
    
    def authenticate(self, db: Session, username: str, password: str) -> Optional[Admin]:
        """验证管理员登录"""
        admin = self.get_by_username(db, username)
        if not admin:
            return None
        if not verify_password(password, admin.password, admin.salt):
            return None
        return admin
    
    def create(self, db: Session, admin_create: AdminCreate) -> Admin:
        """创建管理员"""
        hashed_password, salt = get_password_hash(admin_create.password)
        current_time = int(time.time())
        
        db_admin = Admin(
            username=admin_create.username,
            nickname=admin_create.nickname,
            email=admin_create.email,
            mobile=admin_create.mobile,
            avatar=admin_create.avatar,
            motto=admin_create.motto,
            password=hashed_password,
            salt=salt,
            create_time=current_time,
            update_time=current_time
        )
        db.add(db_admin)
        db.commit()
        db.refresh(db_admin)
        return db_admin
    
    def update(self, db: Session, admin_id: int, admin_update: AdminUpdate) -> Optional[Admin]:
        """更新管理员"""
        admin = self.get_by_id(db, admin_id)
        if not admin:
            return None
        
        update_data = admin_update.model_dump(exclude_unset=True)
        if update_data:
            update_data["update_time"] = int(time.time())
            for field, value in update_data.items():
                setattr(admin, field, value)
            db.commit()
            db.refresh(admin)
        return admin
    
    def get_list(self, db: Session, skip: int = 0, limit: int = 20) -> tuple[List[Admin], int]:
        """获取管理员列表"""
        query = db.query(Admin)
        total = query.count()
        items = query.offset(skip).limit(limit).all()
        return items, total
    
    def delete(self, db: Session, admin_id: int) -> bool:
        """删除管理员"""
        admin = self.get_by_id(db, admin_id)
        if admin:
            db.delete(admin)
            db.commit()
            return True
        return False
    
    def update_login_info(self, db: Session, admin_id: int, login_ip: str):
        """更新登录信息"""
        admin = self.get_by_id(db, admin_id)
        if admin:
            admin.last_login_time = int(time.time())
            admin.last_login_ip = login_ip
            admin.login_failure = 0  # 重置登录失败次数
            db.commit()


class AdminGroupCRUD:
    def get_by_id(self, db: Session, group_id: int) -> Optional[AdminGroup]:
        """根据ID获取管理员组"""
        return db.query(AdminGroup).filter(AdminGroup.id == group_id).first()
    
    def create(self, db: Session, group_create: AdminGroupCreate) -> AdminGroup:
        """创建管理员组"""
        current_time = int(time.time())
        db_group = AdminGroup(
            **group_create.model_dump(),
            create_time=current_time,
            update_time=current_time
        )
        db.add(db_group)
        db.commit()
        db.refresh(db_group)
        return db_group
    
    def update(self, db: Session, group_id: int, group_update: AdminGroupUpdate) -> Optional[AdminGroup]:
        """更新管理员组"""
        group = self.get_by_id(db, group_id)
        if not group:
            return None
        
        update_data = group_update.model_dump(exclude_unset=True)
        if update_data:
            update_data["update_time"] = int(time.time())
            for field, value in update_data.items():
                setattr(group, field, value)
            db.commit()
            db.refresh(group)
        return group
    
    def get_list(self, db: Session) -> List[AdminGroup]:
        """获取管理员组列表"""
        return db.query(AdminGroup).filter(AdminGroup.status == "1").all()
    
    def delete(self, db: Session, group_id: int) -> bool:
        """删除管理员组"""
        group = self.get_by_id(db, group_id)
        if group:
            db.delete(group)
            db.commit()
            return True
        return False


class AdminRuleCRUD:
    def get_by_id(self, db: Session, rule_id: int) -> Optional[AdminRule]:
        """根据ID获取权限规则"""
        return db.query(AdminRule).filter(AdminRule.id == rule_id).first()
    
    def create(self, db: Session, rule_create: AdminRuleCreate) -> AdminRule:
        """创建权限规则"""
        current_time = int(time.time())
        db_rule = AdminRule(
            **rule_create.model_dump(),
            create_time=current_time,
            update_time=current_time
        )
        db.add(db_rule)
        db.commit()
        db.refresh(db_rule)
        return db_rule
    
    def update(self, db: Session, rule_id: int, rule_update: AdminRuleUpdate) -> Optional[AdminRule]:
        """更新权限规则"""
        rule = self.get_by_id(db, rule_id)
        if not rule:
            return None
        
        update_data = rule_update.model_dump(exclude_unset=True)
        if update_data:
            update_data["update_time"] = int(time.time())
            for field, value in update_data.items():
                setattr(rule, field, value)
            db.commit()
            db.refresh(rule)
        return rule
    
    def get_tree(self, db: Session) -> List[AdminRule]:
        """获取权限规则树"""
        return db.query(AdminRule).filter(AdminRule.status == "1").order_by(AdminRule.weigh.desc(), AdminRule.id.asc()).all()
    
    def delete(self, db: Session, rule_id: int) -> bool:
        """删除权限规则"""
        rule = self.get_by_id(db, rule_id)
        if rule:
            db.delete(rule)
            db.commit()
            return True
        return False


# 创建实例
admin_crud = AdminCRUD()
admin_group_crud = AdminGroupCRUD()
admin_rule_crud = AdminRuleCRUD()
