// 管理员相关类型定义

export interface Admin {
  id: number
  username: string
  nickname: string
  email: string
  mobile: string
  avatar: string
  motto: string
  status: string
  login_failure: number
  last_login_time?: number
  last_login_ip: string
  create_time?: number
  update_time?: number
}

export interface AdminListResponse {
  total: number
  items: Admin[]
}

export interface AdminCreate {
  username: string
  nickname: string
  email: string
  mobile: string
  avatar?: string
  motto?: string
  password: string
}

export interface AdminUpdate {
  nickname?: string
  email?: string
  mobile?: string
  avatar?: string
  motto?: string
  status?: string
}

// 管理员组
export interface AdminGroup {
  id: number
  pid: number
  name: string
  rules?: string
  status: string
  create_time?: number
  update_time?: number
}

export interface AdminGroupCreate {
  name: string
  pid?: number
  rules?: string
}

export interface AdminGroupUpdate {
  name?: string
  pid?: number
  rules?: string
  status?: string
}

// 权限规则
export interface AdminRule {
  id: number
  pid: number
  type: string
  title: string
  name: string
  path: string
  icon: string
  menu_type?: string
  url: string
  component: string
  keepalive: number
  extend: string
  remark: string
  weigh: number
  status: string
  create_time?: number
  update_time?: number
  children?: AdminRule[]
}

export interface AdminRuleCreate {
  pid?: number
  type?: string
  title: string
  name: string
  path?: string
  icon?: string
  menu_type?: string
  url?: string
  component?: string
  keepalive?: number
  extend?: string
  remark?: string
  weigh?: number
}

export interface AdminRuleUpdate {
  pid?: number
  type?: string
  title?: string
  name?: string
  path?: string
  icon?: string
  menu_type?: string
  url?: string
  component?: string
  keepalive?: number
  extend?: string
  remark?: string
  weigh?: number
  status?: string
}
