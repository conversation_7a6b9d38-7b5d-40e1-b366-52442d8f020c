import request from '@/utils/request'
import type { 
  Admin, AdminListResponse, AdminCreate, AdminUpdate,
  AdminGroup, AdminGroupCreate, AdminGroupUpdate,
  AdminRule, AdminRuleCreate, AdminRuleUpdate
} from '@/types/admin'

// 管理员相关API
export const getAdmins = (params: { skip?: number; limit?: number }): Promise<AdminListResponse> => {
  return request({
    url: '/api/admin/admins',
    method: 'get',
    params
  })
}

export const createAdmin = (data: AdminCreate): Promise<Admin> => {
  return request({
    url: '/api/admin/admins',
    method: 'post',
    data
  })
}

export const getAdmin = (id: number): Promise<Admin> => {
  return request({
    url: `/api/admin/admins/${id}`,
    method: 'get'
  })
}

export const updateAdmin = (id: number, data: AdminUpdate): Promise<Admin> => {
  return request({
    url: `/api/admin/admins/${id}`,
    method: 'put',
    data
  })
}

export const deleteAdmin = (id: number): Promise<{ message: string }> => {
  return request({
    url: `/api/admin/admins/${id}`,
    method: 'delete'
  })
}

// 管理员组相关API
export const getAdminGroups = (): Promise<AdminGroup[]> => {
  return request({
    url: '/api/admin/groups',
    method: 'get'
  })
}

export const createAdminGroup = (data: AdminGroupCreate): Promise<AdminGroup> => {
  return request({
    url: '/api/admin/groups',
    method: 'post',
    data
  })
}

export const updateAdminGroup = (id: number, data: AdminGroupUpdate): Promise<AdminGroup> => {
  return request({
    url: `/api/admin/groups/${id}`,
    method: 'put',
    data
  })
}

export const deleteAdminGroup = (id: number): Promise<{ message: string }> => {
  return request({
    url: `/api/admin/groups/${id}`,
    method: 'delete'
  })
}

// 权限规则相关API
export const getAdminRules = (): Promise<AdminRule[]> => {
  return request({
    url: '/api/admin/rules',
    method: 'get'
  })
}

export const createAdminRule = (data: AdminRuleCreate): Promise<AdminRule> => {
  return request({
    url: '/api/admin/rules',
    method: 'post',
    data
  })
}

export const updateAdminRule = (id: number, data: AdminRuleUpdate): Promise<AdminRule> => {
  return request({
    url: `/api/admin/rules/${id}`,
    method: 'put',
    data
  })
}

export const deleteAdminRule = (id: number): Promise<{ message: string }> => {
  return request({
    url: `/api/admin/rules/${id}`,
    method: 'delete'
  })
}

// 获取权限信息
export const getAdminPermissions = (): Promise<{ menus: any[]; buttons: any[] }> => {
  return request({
    url: '/api/admin/permissions',
    method: 'get'
  })
}
