from sqlalchemy.orm import Session
from typing import Optional, List
from ..models.user import User
from ..schemas.user import UserCreate, UserUpdate
from ..core.security import get_password_hash, verify_password
import time


class UserCRUD:
    def get_by_username(self, db: Session, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return db.query(User).filter(User.username == username).first()
    
    def get_by_id(self, db: Session, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        return db.query(User).filter(User.id == user_id).first()
    
    def authenticate(self, db: Session, username: str, password: str) -> Optional[User]:
        """验证用户登录"""
        user = self.get_by_username(db, username)
        if not user:
            return None
        if not verify_password(password, user.password, user.salt):
            return None
        return user
    
    def create(self, db: Session, user_create: UserCreate) -> User:
        """创建用户"""
        hashed_password, salt = get_password_hash(user_create.password)
        current_time = int(time.time())
        
        db_user = User(
            username=user_create.username,
            nickname=user_create.nickname,
            email=user_create.email,
            mobile=user_create.mobile,
            avatar=user_create.avatar,
            gender=user_create.gender,
            birthday=user_create.birthday,
            motto=user_create.motto,
            group_id=user_create.group_id,
            password=hashed_password,
            salt=salt,
            status="1",  # 默认启用
            join_time=current_time,
            create_time=current_time,
            update_time=current_time
        )
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        return db_user
    
    def update(self, db: Session, user_id: int, user_update: UserUpdate) -> Optional[User]:
        """更新用户"""
        user = self.get_by_id(db, user_id)
        if not user:
            return None
        
        update_data = user_update.model_dump(exclude_unset=True)
        if update_data:
            update_data["update_time"] = int(time.time())
            for field, value in update_data.items():
                setattr(user, field, value)
            db.commit()
            db.refresh(user)
        return user
    
    def get_list(self, db: Session, skip: int = 0, limit: int = 20, search: str = None) -> tuple[List[User], int]:
        """获取用户列表"""
        query = db.query(User)
        
        # 搜索功能
        if search:
            query = query.filter(
                (User.username.like(f"%{search}%")) |
                (User.nickname.like(f"%{search}%")) |
                (User.email.like(f"%{search}%")) |
                (User.mobile.like(f"%{search}%"))
            )
        
        total = query.count()
        items = query.offset(skip).limit(limit).all()
        return items, total
    
    def delete(self, db: Session, user_id: int) -> bool:
        """删除用户"""
        user = self.get_by_id(db, user_id)
        if user:
            db.delete(user)
            db.commit()
            return True
        return False
    
    def update_status(self, db: Session, user_id: int, status: str) -> Optional[User]:
        """更新用户状态"""
        user = self.get_by_id(db, user_id)
        if user:
            user.status = status
            user.update_time = int(time.time())
            db.commit()
            db.refresh(user)
            return user
        return None
    
    def update_login_info(self, db: Session, user_id: int, login_ip: str):
        """更新登录信息"""
        user = self.get_by_id(db, user_id)
        if user:
            user.last_login_time = int(time.time())
            user.last_login_ip = login_ip
            user.login_failure = 0  # 重置登录失败次数
            db.commit()


# 创建实例
user_crud = UserCRUD()
