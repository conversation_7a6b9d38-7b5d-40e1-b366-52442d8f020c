// 用户相关类型定义

export interface User {
  id: number
  group_id: number
  username: string
  nickname: string
  email: string
  mobile: string
  avatar: string
  gender: number
  birthday?: string
  money: number
  score: number
  status: string
  login_failure: number
  last_login_time?: number
  last_login_ip: string
  join_time?: number
  join_ip: string
  create_time?: number
  update_time?: number
  generated_report_times?: number
  all_report_times?: number
  motto: string
}

export interface UserListResponse {
  total: number
  items: User[]
}

export interface UserCreate {
  username: string
  nickname: string
  email: string
  mobile: string
  avatar?: string
  gender?: number
  birthday?: string
  motto?: string
  password: string
  group_id?: number
}

export interface UserUpdate {
  nickname?: string
  email?: string
  mobile?: string
  avatar?: string
  gender?: number
  birthday?: string
  motto?: string
  status?: string
}

export interface UserStatusUpdate {
  status: string
}
