from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from .core.config import settings
from .api import auth, admin, user

# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    description="管理后台API",
    version="1.0.0",
    debug=settings.DEBUG
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(auth.router, prefix="/api/auth", tags=["认证"])
app.include_router(admin.router, prefix="/api/admin", tags=["管理员"])
app.include_router(user.router, prefix="/api/users", tags=["用户"])


@app.get("/")
async def root():
    """根路径"""
    return {"message": "Admin API is running"}


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
