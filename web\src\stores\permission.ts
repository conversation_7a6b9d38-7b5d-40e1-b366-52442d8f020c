import { ref } from 'vue'
import { defineStore } from 'pinia'
import { getAdminPermissions } from '@/api/admin'

export interface MenuItem {
  id: number
  pid: number
  title: string
  name: string
  path: string
  icon: string
  component: string
  type: string
  children?: MenuItem[]
}

export interface ButtonPermission {
  id: number
  name: string
  title: string
}

export const usePermissionStore = defineStore('permission', () => {
  // 状态
  const menus = ref<MenuItem[]>([])
  const buttons = ref<ButtonPermission[]>([])
  const isLoaded = ref(false)

  // 获取权限信息
  const fetchPermissions = async (): Promise<boolean> => {
    try {
      const response = await getAdminPermissions()
      menus.value = buildMenuTree(response.menus)
      buttons.value = response.buttons
      isLoaded.value = true
      return true
    } catch (error) {
      console.error('Fetch permissions failed:', error)
      return false
    }
  }

  // 构建菜单树
  const buildMenuTree = (menuList: any[]): MenuItem[] => {
    const menuMap = new Map<number, MenuItem>()
    const rootMenus: MenuItem[] = []

    // 先创建所有菜单项的映射
    menuList.forEach(menu => {
      menuMap.set(menu.id, {
        ...menu,
        children: []
      })
    })

    // 构建树形结构
    menuList.forEach(menu => {
      const menuItem = menuMap.get(menu.id)!
      if (menu.pid === 0) {
        rootMenus.push(menuItem)
      } else {
        const parent = menuMap.get(menu.pid)
        if (parent) {
          if (!parent.children) {
            parent.children = []
          }
          parent.children.push(menuItem)
        }
      }
    })

    return rootMenus
  }

  // 检查按钮权限
  const hasButtonPermission = (buttonName: string): boolean => {
    return buttons.value.some(button => button.name === buttonName)
  }

  // 重置权限
  const resetPermissions = (): void => {
    menus.value = []
    buttons.value = []
    isLoaded.value = false
  }

  return {
    // 状态
    menus,
    buttons,
    isLoaded,
    
    // 方法
    fetchPermissions,
    hasButtonPermission,
    resetPermissions
  }
})
