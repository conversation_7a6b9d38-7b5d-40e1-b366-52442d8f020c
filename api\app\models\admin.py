from sqlalchemy import Column, Integer, String, Text, Enum, BigInteger
from sqlalchemy.orm import relationship
from ..core.database import Base
import enum


class AdminStatus(str, enum.Enum):
    DISABLED = "0"
    ENABLED = "1"


class Admin(Base):
    """管理员表"""
    __tablename__ = "ba_admin"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    username = Column(String(20), unique=True, nullable=False, default="")
    nickname = Column(String(50), nullable=False, default="")
    avatar = Column(String(255), nullable=False, default="")
    email = Column(String(50), nullable=False, default="")
    mobile = Column(String(11), nullable=False, default="")
    login_failure = Column(Integer, nullable=False, default=0)
    last_login_time = Column(BigInteger, nullable=True)
    last_login_ip = Column(String(50), nullable=False, default="")
    password = Column(String(32), nullable=False, default="")
    salt = Column(String(30), nullable=False, default="")
    motto = Column(String(255), nullable=False, default="")
    status = Column(Enum(AdminStatus), nullable=False, default=AdminStatus.ENABLED)
    update_time = Column(BigInteger, nullable=True)
    create_time = Column(BigInteger, nullable=True)


class AdminGroupStatus(str, enum.Enum):
    DISABLED = "0"
    ENABLED = "1"


class AdminGroup(Base):
    """管理员组表"""
    __tablename__ = "ba_admin_group"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    pid = Column(Integer, nullable=False, default=0)
    name = Column(String(100), nullable=False, default="")
    rules = Column(Text, nullable=True)
    status = Column(Enum(AdminGroupStatus), nullable=False, default=AdminGroupStatus.ENABLED)
    update_time = Column(BigInteger, nullable=True)
    create_time = Column(BigInteger, nullable=True)


class AdminGroupAccess(Base):
    """管理员组权限关联表"""
    __tablename__ = "ba_admin_group_access"
    
    uid = Column(Integer, primary_key=True)
    group_id = Column(Integer, primary_key=True)


class RuleType(str, enum.Enum):
    MENU_DIR = "menu_dir"
    MENU = "menu"
    BUTTON = "button"


class MenuType(str, enum.Enum):
    TAB = "tab"
    LINK = "link"
    IFRAME = "iframe"


class ExtendType(str, enum.Enum):
    NONE = "none"
    ADD_RULES_ONLY = "add_rules_only"
    ADD_MENU_ONLY = "add_menu_only"


class RuleStatus(str, enum.Enum):
    DISABLED = "0"
    ENABLED = "1"


class AdminRule(Base):
    """权限规则表"""
    __tablename__ = "ba_admin_rule"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    pid = Column(Integer, nullable=False, default=0, index=True)
    type = Column(Enum(RuleType), nullable=False, default=RuleType.MENU)
    title = Column(String(50), nullable=False, default="")
    name = Column(String(50), nullable=False, default="")
    path = Column(String(100), nullable=False, default="")
    icon = Column(String(50), nullable=False, default="")
    menu_type = Column(Enum(MenuType), nullable=True)
    url = Column(String(255), nullable=False, default="")
    component = Column(String(100), nullable=False, default="")
    keepalive = Column(Integer, nullable=False, default=0)
    extend = Column(Enum(ExtendType), nullable=False, default=ExtendType.NONE)
    remark = Column(String(255), nullable=False, default="")
    weigh = Column(Integer, nullable=False, default=0)
    status = Column(Enum(RuleStatus), nullable=False, default=RuleStatus.ENABLED)
    update_time = Column(BigInteger, nullable=True)
    create_time = Column(BigInteger, nullable=True)
