# 项目需求规划

## 项目概述

本项目需要构建一个完整的前后端分离系统，包含用户管理、权限管理等核心功能。

## 前端项目 (`./web` 目录)

### 技术栈
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **状态管理**: Pinia
- **UI组件库**: Element Plus

### 核心功能模块

#### 1. Layout 布局功能
- 顶部导航栏
- 侧边菜单栏
- 面包屑导航
- 用户信息展示
- 退出登录功能
- 响应式布局适配

#### 2. 登录功能
- 用户登录表单
- 表单验证
- 登录状态管理
- Token 存储与管理
- 自动登录/记住密码
- 登录状态持久化

#### 3. 权限管理
- 角色权限配置
- 菜单权限控制
- 按钮级权限控制
- 路由权限守卫
- 动态菜单渲染
- 权限验证中间件

#### 4. 用户管理
- 用户列表展示
- 用户信息增删改查
- 用户角色分配
- 用户状态管理
- 批量操作功能
- 搜索筛选功能

## 后端项目 (`./api` 目录)

### 技术栈
- **框架**: FastAPI
- **数据库**: MySQL
- **ORM**: SQLAlchemy (推荐)
- **认证**: JWT Token

### 数据库表结构

#### 核心数据表
- `ba_admin` - 管理员用户表
- `ba_admin_group` - 管理员组表
- `ba_admin_group_access` - 管理员组权限关联表
- `ba_admin_log` - 管理员操作日志表
- `ba_admin_rule` - 权限规则表
- `ba_user` - 普通用户表

### API 接口规划

#### 认证相关接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/refresh` - Token 刷新
- `GET /api/auth/profile` - 获取用户信息

#### 用户管理接口
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `GET /api/users/{id}` - 获取用户详情
- `PUT /api/users/{id}` - 更新用户信息
- `DELETE /api/users/{id}` - 删除用户
- `PUT /api/users/{id}/status` - 更新用户状态

#### 权限管理接口
- `GET /api/admin/groups` - 获取管理员组列表
- `POST /api/admin/groups` - 创建管理员组
- `PUT /api/admin/groups/{id}` - 更新管理员组
- `DELETE /api/admin/groups/{id}` - 删除管理员组
- `GET /api/admin/rules` - 获取权限规则列表
- `POST /api/admin/rules` - 创建权限规则
- `GET /api/admin/permissions` - 获取用户权限信息

#### 日志管理接口
- `GET /api/admin/logs` - 获取操作日志列表
- `POST /api/admin/logs` - 记录操作日志

## 开发计划

### 阶段一：环境搭建
1. 初始化 Vite + Vue3 + TypeScript 项目
2. 配置 Element Plus 和 Pinia
3. 搭建 FastAPI 项目结构
4. 配置数据库连接

### 阶段二：基础功能
1. 实现 Layout 基础布局
2. 完成用户登录功能
3. 实现 JWT 认证机制
4. 建立前后端数据交互

### 阶段三：核心功能
1. 完善权限管理系统
2. 实现用户管理功能
3. 添加操作日志记录
4. 完善错误处理机制

### 阶段四：优化完善
1. 性能优化
2. 安全加固
3. 测试完善
4. 文档编写

## 技术要点

### 前端关键技术
- Vue 3 Composition API
- TypeScript 类型定义
- Pinia 状态管理
- Vue Router 路由守卫
- Axios 请求拦截器
- Element Plus 组件定制

### 后端关键技术
- FastAPI 异步编程
- JWT Token 认证
- SQLAlchemy ORM
- 数据库迁移管理
- 请求参数验证
- 异常处理机制

## 部署配置

### 开发环境
- 前端开发服务器：`http://localhost:3000`
- 后端API服务器：`http://localhost:8000`
- MySQL数据库：`localhost:3306`

### 生产环境
- 静态资源部署
- API服务部署
- 数据库配置
- 环境变量管理

## 项目结构

### 前端目录结构
```
web/
├── src/
│   ├── components/          # 公共组件
│   ├── views/              # 页面组件
│   │   ├── layout/         # 布局相关
│   │   ├── auth/           # 认证相关
│   │   ├── admin/          # 管理相关
│   │   └── user/           # 用户管理
│   ├── router/             # 路由配置
│   ├── stores/             # Pinia 状态管理
│   ├── utils/              # 工具函数
│   ├── api/                # API 接口
│   └── types/              # TypeScript 类型定义
├── public/
└── package.json
```

### 后端目录结构
```
api/
├── app/
│   ├── api/                # API 路由
│   │   ├── auth/           # 认证相关
│   │   ├── admin/          # 管理相关
│   │   └── user/           # 用户管理
│   ├── core/               # 核心配置
│   ├── models/             # 数据模型
│   ├── schemas/            # Pydantic 模型
│   ├── crud/               # 数据库操作
│   ├── utils/              # 工具函数
│   └── main.py             # 应用入口
├── migrations/             # 数据库迁移
├── requirements.txt
└── .env
```|