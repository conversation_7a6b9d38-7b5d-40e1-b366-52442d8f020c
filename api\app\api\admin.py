from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List
from ..core.database import get_db
from ..crud.admin import admin_crud, admin_group_crud, admin_rule_crud
from ..schemas.admin import (
    AdminCreate, AdminUpdate, AdminResponse, AdminListResponse,
    AdminGroupCreate, AdminGroupUpdate, AdminGroupResponse,
    AdminRuleCreate, AdminRuleUpdate, AdminRuleResponse
)
from ..api.deps import get_current_active_admin
from ..models.admin import Admin

router = APIRouter()


# 管理员管理
@router.get("/admins", response_model=AdminListResponse)
async def get_admins(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """获取管理员列表"""
    items, total = admin_crud.get_list(db, skip=skip, limit=limit)
    return AdminListResponse(
        total=total,
        items=[AdminResponse.model_validate(item) for item in items]
    )


@router.post("/admins", response_model=AdminResponse)
async def create_admin(
    admin_create: AdminCreate,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """创建管理员"""
    # 检查用户名是否已存在
    existing_admin = admin_crud.get_by_username(db, admin_create.username)
    if existing_admin:
        raise HTTPException(status_code=400, detail="用户名已存在")
    
    admin = admin_crud.create(db, admin_create)
    return AdminResponse.model_validate(admin)


@router.get("/admins/{admin_id}", response_model=AdminResponse)
async def get_admin(
    admin_id: int,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """获取管理员详情"""
    admin = admin_crud.get_by_id(db, admin_id)
    if not admin:
        raise HTTPException(status_code=404, detail="管理员不存在")
    return AdminResponse.model_validate(admin)


@router.put("/admins/{admin_id}", response_model=AdminResponse)
async def update_admin(
    admin_id: int,
    admin_update: AdminUpdate,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """更新管理员信息"""
    admin = admin_crud.update(db, admin_id, admin_update)
    if not admin:
        raise HTTPException(status_code=404, detail="管理员不存在")
    return AdminResponse.model_validate(admin)


@router.delete("/admins/{admin_id}")
async def delete_admin(
    admin_id: int,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """删除管理员"""
    if admin_id == current_admin.id:
        raise HTTPException(status_code=400, detail="不能删除自己")
    
    success = admin_crud.delete(db, admin_id)
    if not success:
        raise HTTPException(status_code=404, detail="管理员不存在")
    return {"message": "删除成功"}


# 管理员组管理
@router.get("/groups", response_model=List[AdminGroupResponse])
async def get_admin_groups(
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """获取管理员组列表"""
    groups = admin_group_crud.get_list(db)
    return [AdminGroupResponse.model_validate(group) for group in groups]


@router.post("/groups", response_model=AdminGroupResponse)
async def create_admin_group(
    group_create: AdminGroupCreate,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """创建管理员组"""
    group = admin_group_crud.create(db, group_create)
    return AdminGroupResponse.model_validate(group)


@router.put("/groups/{group_id}", response_model=AdminGroupResponse)
async def update_admin_group(
    group_id: int,
    group_update: AdminGroupUpdate,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """更新管理员组"""
    group = admin_group_crud.update(db, group_id, group_update)
    if not group:
        raise HTTPException(status_code=404, detail="管理员组不存在")
    return AdminGroupResponse.model_validate(group)


@router.delete("/groups/{group_id}")
async def delete_admin_group(
    group_id: int,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """删除管理员组"""
    success = admin_group_crud.delete(db, group_id)
    if not success:
        raise HTTPException(status_code=404, detail="管理员组不存在")
    return {"message": "删除成功"}


# 权限规则管理
@router.get("/rules", response_model=List[AdminRuleResponse])
async def get_admin_rules(
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """获取权限规则列表"""
    rules = admin_rule_crud.get_tree(db)
    
    # 构建树形结构
    rule_dict = {}
    root_rules = []
    
    # 先创建所有规则的字典
    for rule in rules:
        rule_response = AdminRuleResponse.model_validate(rule)
        rule_dict[rule.id] = rule_response
        if rule.pid == 0:
            root_rules.append(rule_response)
    
    # 构建父子关系
    for rule in rules:
        if rule.pid != 0 and rule.pid in rule_dict:
            parent = rule_dict[rule.pid]
            child = rule_dict[rule.id]
            if parent.children is None:
                parent.children = []
            parent.children.append(child)
    
    return root_rules


@router.post("/rules", response_model=AdminRuleResponse)
async def create_admin_rule(
    rule_create: AdminRuleCreate,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """创建权限规则"""
    rule = admin_rule_crud.create(db, rule_create)
    return AdminRuleResponse.model_validate(rule)


@router.put("/rules/{rule_id}", response_model=AdminRuleResponse)
async def update_admin_rule(
    rule_id: int,
    rule_update: AdminRuleUpdate,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """更新权限规则"""
    rule = admin_rule_crud.update(db, rule_id, rule_update)
    if not rule:
        raise HTTPException(status_code=404, detail="权限规则不存在")
    return AdminRuleResponse.model_validate(rule)


@router.delete("/rules/{rule_id}")
async def delete_admin_rule(
    rule_id: int,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """删除权限规则"""
    success = admin_rule_crud.delete(db, rule_id)
    if not success:
        raise HTTPException(status_code=404, detail="权限规则不存在")
    return {"message": "删除成功"}


@router.get("/permissions")
async def get_admin_permissions(
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """获取当前管理员的权限信息"""
    # 这里可以根据管理员的组权限来返回具体的权限信息
    # 暂时返回所有权限（后续可以根据实际需求完善）
    rules = admin_rule_crud.get_tree(db)
    
    # 构建菜单权限
    menus = []
    buttons = []
    
    for rule in rules:
        if rule.type == "menu" or rule.type == "menu_dir":
            menus.append({
                "id": rule.id,
                "pid": rule.pid,
                "title": rule.title,
                "name": rule.name,
                "path": rule.path,
                "icon": rule.icon,
                "component": rule.component,
                "type": rule.type
            })
        elif rule.type == "button":
            buttons.append({
                "id": rule.id,
                "name": rule.name,
                "title": rule.title
            })
    
    return {
        "menus": menus,
        "buttons": buttons
    }
