from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime


class AdminBase(BaseModel):
    """管理员基础模式"""
    username: str
    nickname: str
    email: str
    mobile: str
    avatar: Optional[str] = ""
    motto: Optional[str] = ""


class AdminCreate(AdminBase):
    """创建管理员"""
    password: str


class AdminUpdate(BaseModel):
    """更新管理员"""
    nickname: Optional[str] = None
    email: Optional[str] = None
    mobile: Optional[str] = None
    avatar: Optional[str] = None
    motto: Optional[str] = None
    status: Optional[str] = None


class AdminResponse(AdminBase):
    """管理员响应"""
    id: int
    status: str
    login_failure: int
    last_login_time: Optional[int] = None
    last_login_ip: str
    create_time: Optional[int] = None
    update_time: Optional[int] = None
    
    class Config:
        from_attributes = True


class AdminListResponse(BaseModel):
    """管理员列表响应"""
    total: int
    items: List[AdminResponse]


# 管理员组相关
class AdminGroupBase(BaseModel):
    """管理员组基础模式"""
    name: str
    pid: Optional[int] = 0
    rules: Optional[str] = None


class AdminGroupCreate(AdminGroupBase):
    """创建管理员组"""
    pass


class AdminGroupUpdate(BaseModel):
    """更新管理员组"""
    name: Optional[str] = None
    pid: Optional[int] = None
    rules: Optional[str] = None
    status: Optional[str] = None


class AdminGroupResponse(AdminGroupBase):
    """管理员组响应"""
    id: int
    status: str
    create_time: Optional[int] = None
    update_time: Optional[int] = None
    
    class Config:
        from_attributes = True


# 权限规则相关
class AdminRuleBase(BaseModel):
    """权限规则基础模式"""
    pid: Optional[int] = 0
    type: str = "menu"
    title: str
    name: str
    path: Optional[str] = ""
    icon: Optional[str] = ""
    menu_type: Optional[str] = None
    url: Optional[str] = ""
    component: Optional[str] = ""
    keepalive: Optional[int] = 0
    extend: Optional[str] = "none"
    remark: Optional[str] = ""
    weigh: Optional[int] = 0


class AdminRuleCreate(AdminRuleBase):
    """创建权限规则"""
    pass


class AdminRuleUpdate(BaseModel):
    """更新权限规则"""
    pid: Optional[int] = None
    type: Optional[str] = None
    title: Optional[str] = None
    name: Optional[str] = None
    path: Optional[str] = None
    icon: Optional[str] = None
    menu_type: Optional[str] = None
    url: Optional[str] = None
    component: Optional[str] = None
    keepalive: Optional[int] = None
    extend: Optional[str] = None
    remark: Optional[str] = None
    weigh: Optional[int] = None
    status: Optional[str] = None


class AdminRuleResponse(AdminRuleBase):
    """权限规则响应"""
    id: int
    status: str
    create_time: Optional[int] = None
    update_time: Optional[int] = None
    children: Optional[List['AdminRuleResponse']] = []
    
    class Config:
        from_attributes = True


# 解决前向引用问题
AdminRuleResponse.model_rebuild()
