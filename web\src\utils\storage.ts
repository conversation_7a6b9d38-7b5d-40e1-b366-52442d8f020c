/**
 * 本地存储工具类
 */

const TOKEN_KEY = 'admin_token'
const USER_INFO_KEY = 'admin_user_info'

export const storage = {
  // Token相关
  getToken(): string | null {
    return localStorage.getItem(TOKEN_KEY)
  },
  
  setToken(token: string): void {
    localStorage.setItem(TOKEN_KEY, token)
  },
  
  removeToken(): void {
    localStorage.removeItem(TOKEN_KEY)
  },
  
  // 用户信息相关
  getUserInfo(): any {
    const userInfo = localStorage.getItem(USER_INFO_KEY)
    return userInfo ? JSON.parse(userInfo) : null
  },
  
  setUserInfo(userInfo: any): void {
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
  },
  
  removeUserInfo(): void {
    localStorage.removeItem(USER_INFO_KEY)
  },
  
  // 清除所有存储
  clear(): void {
    this.removeToken()
    this.removeUserInfo()
  }
}
