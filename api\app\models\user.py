from sqlalchemy import Column, Integer, String, Date, BigInteger
from ..core.database import Base


class User(Base):
    """普通用户表"""
    __tablename__ = "ba_user"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    group_id = Column(Integer, nullable=False, default=0)
    username = Column(String(32), unique=True, nullable=False, default="")
    nickname = Column(String(50), nullable=False, default="")
    email = Column(String(50), nullable=False, default="")
    mobile = Column(String(11), nullable=False, default="")
    avatar = Column(String(255), nullable=False, default="")
    gender = Column(Integer, nullable=False, default=0)
    birthday = Column(Date, nullable=True)
    money = Column(Integer, nullable=False, default=0)
    score = Column(Integer, nullable=False, default=0)
    last_login_time = Column(BigInteger, nullable=True)
    last_login_ip = Column(String(50), nullable=False, default="")
    login_failure = Column(Integer, nullable=False, default=0)
    join_ip = Column(String(50), nullable=False, default="")
    join_time = Column(BigInteger, nullable=True)
    motto = Column(String(255), nullable=False, default="")
    password = Column(String(32), nullable=False, default="")
    salt = Column(String(30), nullable=False, default="")
    status = Column(String(30), nullable=False, default="")
    update_time = Column(BigInteger, nullable=True)
    create_time = Column(BigInteger, nullable=True)
    generated_report_times = Column(Integer, nullable=True)
    all_report_times = Column(Integer, nullable=True)
