import request from '@/utils/request'
import type { User, UserListResponse, UserCreate, UserUpdate, UserStatusUpdate } from '@/types/user'

// 获取用户列表
export const getUsers = (params: { 
  skip?: number
  limit?: number
  search?: string 
}): Promise<UserListResponse> => {
  return request({
    url: '/api/users/',
    method: 'get',
    params
  })
}

// 创建用户
export const createUser = (data: UserCreate): Promise<User> => {
  return request({
    url: '/api/users/',
    method: 'post',
    data
  })
}

// 获取用户详情
export const getUser = (id: number): Promise<User> => {
  return request({
    url: `/api/users/${id}`,
    method: 'get'
  })
}

// 更新用户信息
export const updateUser = (id: number, data: UserUpdate): Promise<User> => {
  return request({
    url: `/api/users/${id}`,
    method: 'put',
    data
  })
}

// 删除用户
export const deleteUser = (id: number): Promise<{ message: string }> => {
  return request({
    url: `/api/users/${id}`,
    method: 'delete'
  })
}

// 更新用户状态
export const updateUserStatus = (id: number, data: UserStatusUpdate): Promise<User> => {
  return request({
    url: `/api/users/${id}/status`,
    method: 'put',
    data
  })
}
