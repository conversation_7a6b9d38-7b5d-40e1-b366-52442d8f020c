from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from .config import settings
import hashlib
import secrets

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password: str, hashed_password: str, salt: str) -> bool:
    """验证密码（兼容原有的MD5+盐值加密方式）"""
    # 使用MD5+盐值的方式验证密码
    md5_password = hashlib.md5((plain_password + salt).encode()).hexdigest()
    return md5_password == hashed_password


def get_password_hash(password: str) -> tuple[str, str]:
    """生成密码哈希和盐值（兼容原有的MD5+盐值加密方式）"""
    salt = secrets.token_hex(15)  # 生成30字符的盐值
    hashed_password = hashlib.md5((password + salt).encode()).hexdigest()
    return hashed_password, salt


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[dict]:
    """验证令牌"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        return payload
    except JWTError:
        return None
