from pydantic import BaseModel
from typing import Optional, List
from datetime import date


class UserBase(BaseModel):
    """用户基础模式"""
    username: str
    nickname: str
    email: str
    mobile: str
    avatar: Optional[str] = ""
    gender: Optional[int] = 0
    birthday: Optional[date] = None
    motto: Optional[str] = ""


class UserCreate(UserBase):
    """创建用户"""
    password: str
    group_id: Optional[int] = 0


class UserUpdate(BaseModel):
    """更新用户"""
    nickname: Optional[str] = None
    email: Optional[str] = None
    mobile: Optional[str] = None
    avatar: Optional[str] = None
    gender: Optional[int] = None
    birthday: Optional[date] = None
    motto: Optional[str] = None
    status: Optional[str] = None


class UserResponse(UserBase):
    """用户响应"""
    id: int
    group_id: int
    money: int
    score: int
    status: str
    login_failure: int
    last_login_time: Optional[int] = None
    last_login_ip: str
    join_time: Optional[int] = None
    join_ip: str
    create_time: Optional[int] = None
    update_time: Optional[int] = None
    generated_report_times: Optional[int] = None
    all_report_times: Optional[int] = None
    
    class Config:
        from_attributes = True


class UserListResponse(BaseModel):
    """用户列表响应"""
    total: int
    items: List[UserResponse]


class UserStatusUpdate(BaseModel):
    """用户状态更新"""
    status: str
