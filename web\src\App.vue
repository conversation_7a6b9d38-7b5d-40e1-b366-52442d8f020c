<script setup lang="ts">
import { onMounted } from "vue";
import { useAuthStore } from "@/stores/auth";

const authStore = useAuthStore();

// 应用初始化时检查登录状态
onMounted(async () => {
  await authStore.initAuth();
});
</script>

<template>
  <div id="app">
    <router-view />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial,
    sans-serif;
  overflow-x: hidden;
}

#app {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 确保页面内容不会超出视口 */
body {
  margin: 0;
  padding: 0;
}
</style>
