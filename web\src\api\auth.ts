import request from '@/utils/request'
import type { LoginRequest, LoginResponse, UserInfo } from '@/types/auth'

// 登录
export const login = (data: LoginRequest): Promise<LoginResponse> => {
  return request({
    url: '/api/auth/login',
    method: 'post',
    data
  })
}

// 登出
export const logout = (): Promise<any> => {
  return request({
    url: '/api/auth/logout',
    method: 'post'
  })
}

// 获取用户信息
export const getProfile = (): Promise<UserInfo> => {
  return request({
    url: '/api/auth/profile',
    method: 'get'
  })
}

// 刷新token
export const refreshToken = (): Promise<{ access_token: string; token_type: string }> => {
  return request({
    url: '/api/auth/refresh',
    method: 'post'
  })
}
